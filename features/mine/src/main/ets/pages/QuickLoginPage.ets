import { AccountUtil, buildTitleBar, RouterMap, RouterModule, UserInfo } from 'commonlib';

@Builder
export function QuickLoginPageBuilder() {
  QuickLoginPage();
}

@ComponentV2
export struct QuickLoginPage {
  @Local nextRouter: string = '';

  aboutToAppear(): void {
    this.getRouterParams()
  }

  getRouterParams() {
    const params: Record<string, string> | undefined = RouterModule.getNavParam(RouterMap.QUICK_LOGIN_PAGE)
    if (params?.url) {
      this.nextRouter = params.url
    }
  }

  build() {
    NavDestination() {
      Column() {
        Image($r('app.media.startIcon'))
          .width(80)
          .height(80)
          .margin({ top: 100, bottom: 40 })

        Text('快速登录')
          .fontSize(24)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })

        Button('一键登录')
          .width('80%')
          .height(50)
          .backgroundColor('#007DFF')
          .onClick(() => {
            this.loginSuccess('mock_union_id')
          })
          .margin({ bottom: 20 })

        Row() {
          Text('登录即表示同意')
            .fontSize(12)
            .fontColor('#999999')

          Text('用户协议')
            .fontSize(12)
            .fontColor('#007DFF')
            .onClick(() => {
              RouterModule.push(RouterMap.TERMS_OF_SERVICE_PAGE);
            })

          Text('和')
            .fontSize(12)
            .fontColor('#999999')

          Text('隐私政策')
            .fontSize(12)
            .fontColor('#007DFF')
            .onClick(() => {
              RouterModule.push(RouterMap.PRIVACY_POLICY_DETAIL_PAGE);
            })
        }
      }
      .width('100%')
      .height('100%')
      .justifyContent(FlexAlign.Center)
      .backgroundColor('#F1F3F5')
    }
    .title(buildTitleBar(''))
    .width('100%')
    .height('100%')
    .backgroundColor('#F1F3F5');
  }

  loginSuccess(unionID: string) {
    // 模拟登录
    let data: UserInfo = {
      id: 0,
      avatar: 'default_avatar',
      name: '',
      nickname: '华为用户',
      sex: '',
      cellphone: '',
      birthday: '',
      code: unionID,
      isLogin: true,
      isMembership: false,
    }
    AccountUtil.updateUserInfo(data);
    if (this.nextRouter) {
      RouterModule.replace(this.nextRouter)
    } else {
      RouterModule.pop();
    }
  }
}