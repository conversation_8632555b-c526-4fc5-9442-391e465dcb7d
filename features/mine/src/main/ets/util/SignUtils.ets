import { BusinessError } from '@kit.BasicServicesKit';
import { cryptoFramework } from '@kit.CryptoArchitectureKit';
import { util } from '@kit.ArkTS';
import { hilog } from '@kit.PerformanceAnalysisKit';

// 工具函数
function stringToUint8ArrayWithEncoder(str: string): Uint8Array {
  const encoder = new util.TextEncoder();
  return encoder.encodeInto(str);
}

function uint8ArrayToStringWithDecoder(uint8Array: Uint8Array): string {
  const decoder = new util.TextDecoder();
  return decoder.decodeWithStream(uint8Array, { stream: false });
}

export class SignUtils {
  static async sign(content: string, privateKey: string): Promise<string> {
    return new Promise<string>((resolve, reject) => {
      let keyGenerator = cryptoFramework.createAsyKeyGenerator('RSA2048|PRIMES_2');
      let privateKeyBuffer = new util.Base64Helper().decodeSync(privateKey);
      let privateKeyBlob = new Uint8Array(privateKeyBuffer!);
      keyGenerator.convertKey(null, { data: privateKeyBlob }).then(async pubPair => {
        let signer = cryptoFramework.createSign('RSA2048|PKCS1|SHA256');
        signer.init(pubPair.priKey).then(async () => {
          let input: cryptoFramework.DataBlob = { data: stringToUint8ArrayWithEncoder(content) };
          let signature = await signer.sign(input);
          let result = await new util.Base64Helper().encode(signature.data);
          hilog.debug(0, 'Index', `SignUtils result resolve`);
          resolve(uint8ArrayToStringWithDecoder(result));
        }).catch((error: BusinessError) => {
          hilog.debug(0, 'Index', `SignUtils result reject error ${error.message} code ${error.code}`);
          reject(error.message);
        });

      }).catch((error: BusinessError) => {
        hilog.debug(0, 'Index', `SignUtils result reject error ${error.message} code ${error.code}`);
        reject(error.message);
      });
    });
  }
}