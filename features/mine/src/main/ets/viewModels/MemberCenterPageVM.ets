// TODO: 华为服务 - 取消注释以启用华为支付组件
// import { WxExtraInfo } from 'aggregated_payment';
import { getMemberPackages, MemberPackage } from 'network';

// 临时类型定义，替代华为支付组件中的类型
interface WxExtraInfo {
  partnerId: string;
  appId: string;
  packageValue: string;
  prepayId: string;
  nonceStr: string;
  timeStamp: string;
  sign: string;
  extData: string;
}

@ObservedV2
export class MemberCenterPageVM {
  @Trace memberPackages: MemberPackage[] = [];
  @Trace selectId: number = 0;
  @Trace selectCheckbox: boolean = false;
  @Trace isShow: boolean = false;
  appId: string = '';
  @Trace aliOrderStr: string = '';
  wxOrderReq: WxExtraInfo = {
    partnerId: '',
    appId: '',
    packageValue: '',
    prepayId: '',
    nonceStr: '',
    timeStamp: '',
    sign: '',
    extData: ''
  };
  hwOrderStr: string = '';

  getMemberPackages() {
    getMemberPackages().then(res => {
      if (res.status === 200) {
        this.memberPackages = res.data
        if (res.data.length) {
          this.selectId = res.data[0].id
        }
      }
    })
  }
}
