# 华为服务注释方案 - 模拟器运行支持

## 概述

为了让项目能在模拟器上运行，需要注释掉所有华为服务相关的代码。本文档详细记录了需要注释的代码位置和恢复方法。

## 注释策略

### 1. 权限配置注释

**文件**: `products/entry/src/main/module.json5`

**需要注释的内容**:

```json5
// 注释华为账号追踪权限
{
  "name": "ohos.permission.APP_TRACKING_CONSENT",
  "reason": "$string:app_name",
  "usedScene": {
    "abilities": [
      "EntryAbility"
    ],
    "when": "inuse"
  }
}

// 注释华为账号客户端ID配置
{
  "name": "client_id",
  "value": "xxxx"
}
```

**注释后的代码**:

```json5
"requestPermissions": [
  {
    "name": "ohos.permission.INTERNET",
  }
  // TODO: 华为服务 - 取消注释以启用华为账号追踪权限
  // {
  //   "name": "ohos.permission.APP_TRACKING_CONSENT",
  //   "reason": "$string:app_name",
  //   "usedScene": {
  //     "abilities": [
  //       "EntryAbility"
  //     ],
  //     "when": "inuse"
  //   }
  // }
],
"metadata": [
  // TODO: 华为服务 - 取消注释并配置正确的client_id
  // {
  //   "name": "client_id",
  //   "value": "xxxx"
  // }
]
```

### 2. 华为广告服务注释

**文件**: `components/aggregated_ads/src/main/ets/components/HwAdService.ets`

**需要注释的导入**:

```typescript
// TODO: 华为服务 - 取消注释以启用华为广告服务
// import { AdComponent, advertising, identifier } from '@kit.AdsKit';
// import { abilityAccessCtrl, common } from '@kit.AbilityKit';
```

**需要注释的核心方法**:

- `requestOAIDTrackingConsentPermissions()` 方法
- `requestAd()` 方法
- `aboutToAppear()` 中的华为广告初始化代码

### 3. 华为登录服务注释

**文件**: `components/aggregated_login/src/main/ets/components/LoginService.ets`

**需要注释的导入**:

```typescript
// TODO: 华为服务 - 取消注释以启用华为账号登录
// import { authentication, loginComponentManager, LoginWithHuaweiIDButton } from '@kit.AccountKit';
```

**需要注释的核心代码**:

- `LoginWithHuaweiIDButtonController` 相关代码
- `handleLoginWithHuaweiIDButton()` 方法
- `getQuickLoginAnonymousPhone()` 方法

### 4. 华为支付服务注释

**文件**: `components/aggregated_payment/src/main/ets/viewmodel/AggregatedPaymentVM.ets`

**需要注释的导入**:

```typescript
// TODO: 华为服务 - 取消注释以启用华为支付服务
// import { paymentService } from '@kit.PaymentKit';
```

**需要注释的方法**:

- `huaweiPay()` 方法

### 5. 广告页面注释

**文件**: `products/entry/src/main/ets/pages/LaunchAdPage.ets`

**需要注释的组件调用**:

```typescript
// TODO: 华为服务 - 取消注释以启用开屏广告
// AdServicePage({
//   channelType: ChannelType.HUAWEI_AD,
//   adId: 'testq6zq98hecj',
//   adType: AdType.SPLASH_AD,
//   closeCallBack: () => {
//     this.jumpUrl()
//   },
// })

// 临时替代方案 - 直接跳转
Column() {
  Text('广告加载中...')
    .fontSize(16)
    .margin({ top: 100 })

  Button('跳过广告')
    .onClick(() => {
      this.jumpUrl()
    })
    .margin({ top: 20 })
}.width('100%').height('100%').justifyContent(FlexAlign.Center)
```

## 依赖配置注释

### 1. 包依赖注释

**文件**: `products/entry/oh-package.json5`

**需要注释的依赖**:

```json5
"dependencies": {
  "commonlib": 'file:../../commons/commonlib',
  "home": 'file:../../features/home',
  "classification": 'file:../../features/classification',
  "calories": 'file:../../features/calories',
  "mine": 'file:../../features/mine',
  "network": 'file:../../commons/network',
  // TODO: 华为服务 - 取消注释以启用华为广告组件
  // "aggregated_ads": 'file:../../components/aggregated_ads',
}
```

### 2. 构建配置注释

**文件**: `build-profile.json5`

需要在 modules 数组中注释华为服务相关模块:

```json5
"modules": [
  // ... 其他模块
  // TODO: 华为服务 - 取消注释以启用华为服务组件
  // {
  //   "name": "aggregated_ads",
  //   "srcPath": "./components/aggregated_ads"
  // },
  // {
  //   "name": "aggregated_login",
  //   "srcPath": "./components/aggregated_login"
  // },
  // {
  //   "name": "aggregated_payment",
  //   "srcPath": "./components/aggregated_payment"
  // }
]
```

## 功能替代方案

### 1. 登录功能替代

在需要登录的地方，可以使用模拟登录:

```typescript
// 模拟登录成功
private mockLogin() {
  // 设置模拟用户信息
  const mockUser = {
    id: 12345,
    name: '测试用户',
    avatar: '',
    isLogin: true
  };

  // 保存到本地存储
  PreferenceUtil.getInstance().put('userInfo', JSON.stringify(mockUser));

  // 触发登录成功回调
  this.loginFinishedCb(true, 'mock_union_id');
}
```

### 2. 支付功能替代

```typescript
// 模拟支付成功
private mockPayment() {
  setTimeout(() => {
    this.paySuccessEvent(ChannelType.HUAWEI_PAY);
  }, 1000);
}
```

### 3. 广告功能替代

```typescript
// 模拟广告加载完成
private mockAdLoaded() {
  setTimeout(() => {
    this.closeCallBack();
  }, 3000); // 3秒后自动关闭
}
```

## 恢复华为服务的步骤

当需要恢复华为服务时，按以下步骤操作:

1. **恢复权限配置**: 取消注释 `module.json5` 中的权限和 metadata 配置
2. **恢复依赖**: 取消注释 `oh-package.json5` 和 `build-profile.json5` 中的华为服务依赖
3. **恢复导入语句**: 取消注释所有 `@kit.AccountKit`、`@kit.AdsKit`、`@kit.PaymentKit` 相关导入
4. **恢复服务调用**: 取消注释华为服务的具体调用代码
5. **配置服务参数**:
   - 配置正确的 `client_id`
   - 配置正确的广告位 `adId`
   - 配置支付相关参数
6. **重新构建项目**: 执行 `hvigor clean` 和 `hvigor build`

## 注意事项

1. **标记规范**: 所有注释都使用 `// TODO: 华为服务 - ` 前缀，便于搜索和管理
2. **功能完整性**: 注释华为服务后，确保应用的核心功能仍然可用
3. **测试验证**: 注释后需要在模拟器上完整测试应用流程
4. **版本控制**: 建议创建专门的分支来管理模拟器版本的代码

## 已完成的修改清单

### ✅ 权限配置修改

- [x] `products/entry/src/main/module.json5` - 注释华为账号权限和 client_id

### ✅ 依赖配置修改

- [x] `build-profile.json5` - 注释华为服务组件模块
- [x] `products/entry/oh-package.json5` - 注释华为广告组件依赖
- [x] `features/mine/oh-package.json5` - 注释华为登录和支付组件依赖

### ✅ 页面代码修改

- [x] `products/entry/src/main/ets/pages/LaunchAdPage.ets` - 注释广告组件，添加模拟广告页面
- [x] `features/mine/src/main/ets/pages/QuickLoginPage.ets` - 注释登录组件，添加模拟登录页面
- [x] `features/mine/src/main/ets/pages/MemberCenterPage.ets` - 注释支付组件，添加模拟支付功能
- [x] `features/mine/src/main/ets/util/MockApi.ets` - 添加临时类型定义
- [x] `features/mine/src/main/ets/viewModels/MemberCenterPageVM.ets` - 注释支付组件导入，添加临时类型定义
- [x] `features/mine/src/main/ets/util/SignUtils.ets` - 注释支付宝 SDK 导入，添加临时工具函数

### ✅ 模拟功能实现

- [x] 模拟广告页面 - 显示"广告加载中"和跳过按钮
- [x] 模拟登录功能 - 一键登录按钮，模拟用户信息
- [x] 模拟支付功能 - 直接模拟支付成功，更新会员状态

## 项目状态

**当前状态**: ✅ 已完成华为服务注释，项目应该可以在模拟器上运行

**测试建议**:

1. 在 DevEco Studio 中打开项目
2. 执行 `Build` -> `Clean Project` 清理缓存
3. 选择 HarmonyOS 模拟器作为运行目标
4. 点击运行按钮测试应用启动
5. 测试各个页面的基本功能
6. 测试模拟登录和支付功能

**如果遇到构建错误**:

- 删除 `.hvigor` 目录
- 在 DevEco Studio 中执行 `Build` -> `Rebuild Project`
- 确保所有华为服务相关的导入语句都已注释

## 搜索关键词

在 IDE 中可以使用以下关键词快速定位需要注释的代码:

- `TODO: 华为服务` - 查找所有注释的华为服务代码
- `@kit.AccountKit`
- `@kit.AdsKit`
- `@kit.PaymentKit`
- `advertising`
- `authentication`
- `paymentService`
- `LoginWithHuaweiIDButton`
- `AdComponent`
- `client_id`
- `APP_TRACKING_CONSENT`
