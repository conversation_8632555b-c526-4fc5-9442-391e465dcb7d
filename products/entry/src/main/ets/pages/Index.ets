import { CommonConstants, PreferenceUtil, RouterMap, RouterModule } from 'commonlib';

@Entry
@ComponentV2
struct Index {
  navStack: NavPathStack = RouterModule.getStack();

  aboutToAppear(): void {
    // 检查隐私政策是否已同意，直接跳转到相应页面
    let privacyState = PreferenceUtil.getInstance().get(CommonConstants.PRIVACY_POLICY_KEY, '0') as string
    if (privacyState === '1') {
      RouterModule.replace(RouterMap.MAIN_ENTRY)
    } else {
      RouterModule.replace(RouterMap.PRIVACY_POLICY_PAGE)
    }
  }

  build() {
    Navigation(this.navStack) {
    }
    .hideTitleBar(true)
    .hideNavBar(true)
    .mode(NavigationMode.Stack)
    .height(CommonConstants.FULL_HEIGHT)
    .width(CommonConstants.FULL_WIDTH);
  }
}
