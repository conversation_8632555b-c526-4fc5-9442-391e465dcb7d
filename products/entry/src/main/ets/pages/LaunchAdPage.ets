import { CommonConstants, PreferenceUtil, RouterMap, RouterModule } from 'commonlib';

@Builder
export function LaunchAdPageBuilder() {
  LaunchAdPage()
}

@ComponentV2
struct LaunchAdPage {
  aboutToAppear(): void {
    // 直接跳转，不显示广告页面
    setTimeout(() => {
      this.jumpUrl()
    }, 1000) // 1秒后跳转
  }

  build() {
    NavDestination() {
      Column() {
        Image($r('app.media.startIcon'))
          .width(120)
          .height(120)
          .margin({ top: 200 })

        Text('菜谱模板')
          .fontSize(24)
          .fontWeight(FontWeight.Bold)
          .margin({ top: 20 })
          .fontColor('#333333')
      }
      .width('100%')
      .height('100%')
      .justifyContent(FlexAlign.Center)
      .backgroundColor('#F5F5F5')
    }
    .hideTitleBar(true)
  }

  jumpUrl() {
    let privacyState = PreferenceUtil.getInstance().get(CommonConstants.PRIVACY_POLICY_KEY, '0') as string
    if (privacyState === '1') {
      RouterModule.replace(RouterMap.MAIN_ENTRY)
    } else {
      RouterModule.replace(RouterMap.PRIVACY_POLICY_PAGE)
    }
  }
}