// TODO: 华为服务 - 取消注释以启用华为广告服务
// import { AdServicePage, AdType, ChannelType } from 'aggregated_ads';
import { CommonConstants, PreferenceUtil, RouterMap, RouterModule } from 'commonlib';

@Builder
export function LaunchAdPageBuilder() {
  LaunchAdPage()
}

@ComponentV2
struct LaunchAdPage {
  build() {
    NavDestination() {
      Column() {
        // TODO: 华为服务 - 取消注释以启用开屏广告
        // AdServicePage({
        //   channelType: ChannelType.HUAWEI_AD,
        //   adId: 'testq6zq98hecj',
        //   adType: AdType.SPLASH_AD,
        //   closeCallBack: () => {
        //     this.jumpUrl()
        //   },
        // })

        // 临时替代方案 - 模拟广告页面
        Column() {
          Text('广告加载中...')
            .fontSize(16)
            .margin({ top: 100 })

          Button('跳过广告')
            .onClick(() => {
              this.jumpUrl()
            })
            .margin({ top: 20 })
        }.width('100%').height('100%').justifyContent(FlexAlign.Center)
      }.width('100%')
      .height('100%');
    }
    .hideTitleBar(true)
  }

  jumpUrl() {
    let privacyState = PreferenceUtil.getInstance().get(CommonConstants.PRIVACY_POLICY_KEY, '0') as string
    if (privacyState === '1') {
      RouterModule.replace(RouterMap.MAIN_ENTRY)
    } else {
      RouterModule.replace(RouterMap.PRIVACY_POLICY_PAGE)
    }
  }
}