{"meta": {"stableOrder": true, "enableUnifiedLockfile": false}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"@ohos/axios@^2.2.4": "@ohos/axios@2.2.6", "@ohos/mpchart@3.0.21": "@ohos/mpchart@3.0.21", "base_ui@../../components/base_ui": "base_ui@../../components/base_ui", "calorie_calculation@../../components/calorie_calculation": "calorie_calculation@../../components/calorie_calculation", "calories@../../features/calories": "calories@../../features/calories", "classification@../../features/classification": "classification@../../features/classification", "commonlib@../../commons/commonlib": "commonlib@../../commons/commonlib", "featured_recipes@../../components/featured_recipes": "featured_recipes@../../components/featured_recipes", "home@../../features/home": "home@../../features/home", "home_search@../../components/home_search": "home_search@../../components/home_search", "link_category@../../components/link_category": "link_category@../../components/link_category", "mine@../../features/mine": "mine@../../features/mine", "network@../../commons/network": "network@../../commons/network", "personal_homepage@../../components/personal_homepage": "personal_homepage@../../components/personal_homepage", "shopping_basket@../../components/shopping_basket": "shopping_basket@../../components/shopping_basket", "upload_recipe@../../components/upload_recipe": "upload_recipe@../../components/upload_recipe"}, "packages": {"@ohos/axios@2.2.6": {"name": "@ohos/axios", "version": "2.2.6", "integrity": "sha512-A1JqGe6XaeqWyjQETitFW4EkubQS7Fv7h0YG5a/ry3/a/vOgVGzwC4y5KAhvMzVv1tYjfY0ntMtV2kJGlmOHcQ==", "resolved": "https://ohpm.openharmony.cn/ohpm/@ohos/axios/-/axios-2.2.6.har", "registryType": "ohpm"}, "@ohos/mpchart@3.0.21": {"name": "@ohos/mpchart", "version": "3.0.21", "integrity": "sha512-3Ozqe/hIx4kwjxTsgeb07elKwa4K43mO//XRknsLdLIbz/oQsRNGKvNQI0Ooy3tfrkTofQOI5kW1rA6gUJQDyg==", "resolved": "https://ohpm.openharmony.cn/ohpm/@ohos/mpchart/-/mpchart-3.0.21.har", "registryType": "ohpm"}, "base_ui@../../components/base_ui": {"name": "base_ui", "version": "1.0.0", "resolved": "../../components/base_ui", "registryType": "local"}, "calorie_calculation@../../components/calorie_calculation": {"name": "calorie_calculation", "version": "1.0.0", "resolved": "../../components/calorie_calculation", "registryType": "local", "dependencies": {"@ohos/mpchart": "3.0.21", "base_ui": "file:../../components/base_ui"}}, "calories@../../features/calories": {"name": "calories", "version": "1.0.0", "resolved": "../../features/calories", "registryType": "local", "dependencies": {"commonlib": "file:../../commons/commonlib", "network": "file:../../commons/network", "calorie_calculation": "file:../../components/calorie_calculation"}}, "classification@../../features/classification": {"name": "classification", "version": "1.0.0", "resolved": "../../features/classification", "registryType": "local", "dependencies": {"commonlib": "file:../../commons/commonlib", "network": "file:../../commons/network", "link_category": "file:../../components/link_category", "base_ui": "file:../../components/base_ui", "shopping_basket": "file:../../components/shopping_basket"}}, "commonlib@../../commons/commonlib": {"name": "commonlib", "version": "1.0.0", "resolved": "../../commons/commonlib", "registryType": "local"}, "featured_recipes@../../components/featured_recipes": {"name": "featured_recipes", "version": "1.0.0", "resolved": "../../components/featured_recipes", "registryType": "local"}, "home@../../features/home": {"name": "home", "version": "1.0.0", "resolved": "../../features/home", "registryType": "local", "dependencies": {"commonlib": "file:../../commons/commonlib", "network": "file:../../commons/network", "base_ui": "file:../../components/base_ui", "featured_recipes": "file:../../components/featured_recipes", "home_search": "file:../../components/home_search", "personal_homepage": "file:../../components/personal_homepage"}}, "home_search@../../components/home_search": {"name": "home_search", "version": "1.0.0", "resolved": "../../components/home_search", "registryType": "local"}, "link_category@../../components/link_category": {"name": "link_category", "version": "1.0.0", "resolved": "../../components/link_category", "registryType": "local"}, "mine@../../features/mine": {"name": "mine", "version": "1.0.0", "resolved": "../../features/mine", "registryType": "local", "dependencies": {"commonlib": "file:../../commons/commonlib", "network": "file:../../commons/network", "base_ui": "file:../../components/base_ui", "featured_recipes": "file:../../components/featured_recipes", "upload_recipe": "file:../../components/upload_recipe", "personal_homepage": "file:../../components/personal_homepage"}}, "network@../../commons/network": {"name": "network", "version": "1.0.0", "resolved": "../../commons/network", "registryType": "local", "dependencies": {"@ohos/axios": "^2.2.4"}}, "personal_homepage@../../components/personal_homepage": {"name": "personal_homepage", "version": "1.0.0", "resolved": "../../components/personal_homepage", "registryType": "local", "dependencies": {"base_ui": "file:../base_ui", "featured_recipes": "file:../featured_recipes"}}, "shopping_basket@../../components/shopping_basket": {"name": "shopping_basket", "version": "1.0.0", "resolved": "../../components/shopping_basket", "registryType": "local", "dependencies": {"base_ui": "file:../base_ui"}}, "upload_recipe@../../components/upload_recipe": {"name": "upload_recipe", "version": "1.0.0", "resolved": "../../components/upload_recipe", "registryType": "local"}}}