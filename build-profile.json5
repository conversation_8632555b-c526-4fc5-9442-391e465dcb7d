{
  app: {
    signingConfigs: [],
    products: [
      {
        name: "default",
        signingConfig: "default",
        compatibleSdkVersion: "5.0.4(16)",
        runtimeOS: "HarmonyOS",
        buildOption: {
          strictMode: {
            caseSensitiveCheck: true,
            useNormalizedOHMUrl: true,
          },
        },
      },
    ],
    buildModeSet: [
      {
        name: "debug",
      },
      {
        name: "release",
      },
    ],
  },
  modules: [
    {
      name: "entry",
      srcPath: "./products/entry",
      targets: [
        {
          name: "default",
          applyToProducts: ["default"],
        },
      ],
    },
    {
      name: "commonlib",
      srcPath: "./commons/commonlib",
    },
    {
      name: "classification",
      srcPath: "./features/classification",
    },
    {
      name: "home",
      srcPath: "./features/home",
    },
    {
      name: "mine",
      srcPath: "./features/mine",
    },
    {
      name: "network",
      srcPath: "./commons/network",
    },
    {
      name: "link_category",
      srcPath: "./components/link_category",
    },
    {
      name: "featured_recipes",
      srcPath: "./components/featured_recipes",
    },
    {
      name: "home_search",
      srcPath: "./components/home_search",
    },
    {
      name: "calories",
      srcPath: "./features/calories",
    },
    {
      name: "calorie_calculation",
      srcPath: "./components/calorie_calculation",
    },

    {
      name: "base_ui",
      srcPath: "./components/base_ui",
    },
    {
      name: "shopping_basket",
      srcPath: "./components/shopping_basket",
    },
    {
      name: "upload_recipe",
      srcPath: "./components/upload_recipe",
    },
    {
      name: "personal_homepage",
      srcPath: "./components/personal_homepage",
    },
  ],
}
