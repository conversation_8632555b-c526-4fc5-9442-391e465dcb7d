# 模拟器运行测试指南

## 测试前准备

### 1. 清理构建缓存

由于命令行工具可能未配置，建议在 DevEco Studio 中操作：

**方法一：使用 DevEco Studio**

- 打开 DevEco Studio
- 选择 `Build` -> `Clean Project`
- 等待清理完成

**方法二：手动清理（如果需要）**

```bash
# 删除构建缓存目录
rm -rf .hvigor
rm -rf oh_modules
rm -rf oh-package-lock.json5
rm -rf features/mine/oh_modules
rm -rf features/mine/oh-package-lock.json5
```

### 2. 重新构建项目

在 DevEco Studio 中：

- 选择 `Build` -> `Rebuild Project`
- 或者直接点击运行按钮，系统会自动构建

### 3. 检查模拟器状态

确保 HarmonyOS 模拟器已启动并连接成功。

## 构建测试

### 1. 在 DevEco Studio 中构建和运行

1. 打开 DevEco Studio
2. 导入项目（如果尚未导入）
3. 等待项目索引完成
4. 选择 HarmonyOS 模拟器作为运行目标
5. 点击运行按钮（绿色三角形）或使用快捷键 `Shift + F10`

### 2. 预期结果

- 项目应该能够成功构建，没有编译错误
- 应用能够在模拟器上启动
- 显示模拟广告页面，可以跳过

## 功能测试清单

### ✅ 基础功能测试

- [ ] **应用启动**: 应用能正常启动，显示启动页面
- [ ] **广告页面**: 显示模拟广告页面，3 秒后可跳过
- [ ] **主页导航**: 底部导航栏正常显示，可切换页面
- [ ] **首页**: 菜谱列表正常显示
- [ ] **分类页**: 分类列表正常显示
- [ ] **热量计算**: 热量计算功能正常
- [ ] **我的页面**: 个人中心页面正常显示

### ✅ 登录功能测试

- [ ] **模拟登录**: 点击登录按钮显示模拟登录页面
- [ ] **登录成功**: 模拟登录成功，用户状态更新
- [ ] **登录状态**: 登录后显示用户信息
- [ ] **退出登录**: 能正常退出登录

### ✅ 支付功能测试

- [ ] **会员中心**: 会员中心页面正常显示
- [ ] **模拟支付**: 点击开通会员显示模拟支付成功
- [ ] **会员状态**: 支付成功后会员状态更新

### ❌ 已禁用的华为服务功能

- [x] **华为广告**: 已注释，使用模拟广告页面
- [x] **华为登录**: 已注释，使用模拟登录
- [x] **华为支付**: 已注释，使用模拟支付
- [x] **权限申请**: 华为相关权限已注释

## 常见问题排查

### 1. 构建失败

**问题**: 找不到华为服务相关的模块
**解决**: 检查以下文件是否正确注释了华为服务依赖：

- `build-profile.json5`
- `products/entry/oh-package.json5`
- `features/mine/oh-package.json5`

### 2. 导入错误

**问题**: 找不到 `aggregated_ads`、`aggregated_login`、`aggregated_payment` 模块
**解决**: 检查相关页面是否正确注释了导入语句：

- `products/entry/src/main/ets/pages/LaunchAdPage.ets`
- `features/mine/src/main/ets/pages/QuickLoginPage.ets`
- `features/mine/src/main/ets/pages/MemberCenterPage.ets`

### 3. 权限错误

**问题**: 华为账号相关权限错误
**解决**: 检查 `products/entry/src/main/module.json5` 中的权限配置是否正确注释

### 4. 运行时错误

**问题**: 应用启动后崩溃
**解决**:

1. 检查日志输出，定位具体错误
2. 确认所有华为服务调用都已注释
3. 检查模拟替代方案是否正确实现

## 性能测试

### 1. 启动时间

- 冷启动时间应在 3 秒内
- 热启动时间应在 1 秒内

### 2. 内存使用

- 应用运行时内存使用应在合理范围内
- 无明显内存泄漏

### 3. 响应性

- 页面切换流畅，无卡顿
- 按钮点击响应及时

## 测试报告模板

```
测试日期: ____
测试环境: HarmonyOS模拟器 版本____
DevEco Studio版本: ____

基础功能测试:
- 应用启动: ✅/❌
- 页面导航: ✅/❌
- 数据显示: ✅/❌

模拟功能测试:
- 模拟登录: ✅/❌
- 模拟支付: ✅/❌
- 模拟广告: ✅/❌

问题记录:
1. ____
2. ____

总体评价: ✅通过/❌失败
```

## 恢复华为服务

当需要在真机上测试华为服务时，按以下步骤恢复：

1. **搜索注释标记**
   在 IDE 中搜索 `TODO: 华为服务` 找到所有注释的代码

2. **取消注释**
   按照 `华为服务注释方案.md` 文档逐步取消注释

3. **配置服务参数**

   - 配置正确的 `client_id`
   - 配置广告位 `adId`
   - 配置支付相关参数

4. **重新构建**
   ```bash
   hvigor clean
   ohpm install
   hvigor build
   ```

## 注意事项

1. **版本控制**: 建议为模拟器版本创建独立分支
2. **代码标记**: 所有注释都使用统一的 `TODO: 华为服务` 标记
3. **功能完整性**: 确保核心业务功能在模拟器上仍然可用
4. **测试覆盖**: 定期测试模拟器版本的功能完整性
